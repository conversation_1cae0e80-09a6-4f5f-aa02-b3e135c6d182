{"version": 2, "dgSpecHash": "jNqxwAAC8F4=", "success": true, "projectFilePath": "E:\\Plugins DEV\\MySecondPlugin\\MySecondPlugin.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\bepinex.analyzers\\1.0.8\\bepinex.analyzers.1.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bepinex.baselib\\5.4.20\\bepinex.baselib.5.4.20.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bepinex.core\\5.4.21\\bepinex.core.5.4.21.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bepinex.plugininfoprops\\2.1.0\\bepinex.plugininfoprops.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harmonyx\\2.7.0\\harmonyx.2.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netframework.referenceassemblies\\1.0.2\\microsoft.netframework.referenceassemblies.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netframework.referenceassemblies.net46\\1.0.2\\microsoft.netframework.referenceassemblies.net46.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mono.cecil\\0.11.4\\mono.cecil.0.11.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\monomod.runtimedetour\\21.12.13.1\\monomod.runtimedetour.21.12.13.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\monomod.utils\\21.12.13.1\\monomod.utils.21.12.13.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\unityengine.modules\\2018.4.11\\unityengine.modules.2018.4.11.nupkg.sha512"], "logs": []}