﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.netframework.referenceassemblies.net46\1.0.2\build\Microsoft.NETFramework.ReferenceAssemblies.net46.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.netframework.referenceassemblies.net46\1.0.2\build\Microsoft.NETFramework.ReferenceAssemblies.net46.targets')" />
    <Import Project="$(NuGetPackageRoot)bepinex.core\5.4.21\build\BepInEx.Core.targets" Condition="Exists('$(NuGetPackageRoot)bepinex.core\5.4.21\build\BepInEx.Core.targets')" />
  </ImportGroup>
</Project>