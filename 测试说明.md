# 动画控制功能测试说明

## 功能概述

现在插件已经成功集成了独立的动画控制系统，主要功能包括：

1. **自动动画检测** - 根据角色移动自动播放行走/待机动画
2. **手动动画控制** - 通过UI按钮手动切换动画状态
3. **动画参数调节** - 可调整动画播放速度和检测阈值
4. **模块化设计** - 动画控制逻辑独立于主插件文件

## 文件结构

```
MySecondPlugin/
├── Plugin.cs                          # 主插件文件
├── CharacterAnimationController.cs    # 独立的动画控制器类
├── AnimationController_Usage.md       # 使用说明文档
└── 测试说明.md                        # 本文件
```

## 测试步骤

### 1. 编译和加载插件

1. 确保所有依赖项正确引用
2. 编译插件生成DLL文件
3. 将DLL放入BepInEx/plugins目录
4. 启动HS2 Studio

### 2. 基本功能测试

1. **打开UI界面**
   - 按F3键打开角色控制UI
   - 确认界面显示正常，包含新增的"动画控制"区域

2. **角色选择测试**
   - 在Studio中添加一个角色
   - 确认UI显示角色信息
   - 检查动画控制器状态显示

### 3. 自动动画测试

1. **启用自动动画**
   - 确认"自动行走动画"复选框已勾选
   - 观察动画状态显示

2. **移动检测测试**
   - 使用移动控制功能（直线移动、圆形移动等）
   - 观察角色开始移动时是否自动播放行走动画
   - 观察角色停止移动时是否自动切换到待机动画

3. **阈值调整测试**
   - 调整动画速度滑块
   - 观察动画播放速度变化

### 4. 手动动画测试

1. **禁用自动动画**
   - 取消勾选"自动行走动画"复选框

2. **手动切换测试**
   - 点击"待机"按钮，观察角色动画
   - 点击"行走"按钮，观察角色动画
   - 点击"跑步"按钮，观察角色动画

3. **演示功能测试**
   - 点击"演示动画循环"按钮
   - 观察动画是否按顺序循环切换

### 5. 综合功能测试

1. **移动+动画组合**
   - 启用自动动画
   - 使用第一人称模式移动角色
   - 观察WASD移动时的动画变化

2. **状态信息验证**
   - 检查状态信息区域显示是否准确
   - 验证移动状态和动画状态的同步

## 预期结果

### 自动动画功能
- ✅ 角色开始移动时自动播放行走动画
- ✅ 角色停止移动时自动切换到待机动画
- ✅ 动画切换平滑，无明显延迟
- ✅ 控制台输出相应的日志信息

### 手动控制功能
- ✅ 三个动画按钮都能正确切换动画
- ✅ 动画速度滑块能实时调整播放速度
- ✅ 演示功能能循环播放不同动画

### UI界面
- ✅ 动画控制区域正确显示
- ✅ 状态信息实时更新
- ✅ 所有控件响应正常

## 故障排除

### 常见问题

1. **动画不播放**
   - 检查角色是否正确选择
   - 查看控制台是否有错误信息
   - 确认角色支持LoadAnime方法

2. **自动检测不工作**
   - 确认"自动行走动画"已启用
   - 检查移动是否足够明显（超过阈值）
   - 尝试手动移动角色测试

3. **UI显示异常**
   - 检查窗口大小是否合适
   - 确认所有UI元素都在可见范围内

### 调试信息

插件会在控制台输出以下调试信息：
- 角色选择变化
- 动画状态切换
- 移动检测结果
- 错误和异常信息

## 开发者接口

### 其他开发者如何使用

1. **引用动画控制器**
```csharp
// 创建实例
var animController = new CharacterAnimationController(Logger);

// 设置目标角色
animController.SetTargetCharacter(character);

// 在Update中调用
animController.Update();
```

2. **自定义配置**
```csharp
// 调整参数
animController.SetAutoWalkAnimation(true);
animController.SetAnimationSpeed(1.5f);
animController.SetMoveThreshold(0.02f);
```

3. **状态监控**
```csharp
// 获取状态
var state = animController.CurrentAnimationState;
var isMoving = animController.IsMoving;
var statusInfo = animController.GetStatusInfo();
```

## 扩展建议

### 可能的改进方向

1. **更多动画状态**
   - 添加跳跃、坐下等动画
   - 支持自定义动画序列

2. **高级检测**
   - 基于速度的动画选择
   - 方向感知的动画切换

3. **配置持久化**
   - 保存用户设置
   - 角色特定的动画配置

4. **性能优化**
   - 减少不必要的计算
   - 批量处理多角色动画

## 总结

动画控制功能已成功模块化，提供了：
- 🎯 **独立的动画控制类** - 便于其他开发者调用
- 🔄 **自动和手动双模式** - 满足不同使用需求  
- 📊 **完整的状态监控** - 便于调试和监控
- 📚 **详细的使用文档** - 降低使用门槛

插件现在具备了完整的角色动画控制能力，可以根据角色移动状态智能地播放相应动画，同时保持了良好的代码结构和可扩展性。
