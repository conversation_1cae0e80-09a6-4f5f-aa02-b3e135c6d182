using AIChara;
using BepInEx.Logging;
using Studio;
using UnityEngine;
using System;

/// <summary>
/// 角色动画控制器 - 提供角色动画的自动控制和手动控制功能
/// </summary>
public class CharacterAnimationController
{
    private ManualLogSource logger;
    
    // 动画状态枚举
    public enum AnimationState
    {
        Idle,       // 待机
        Walking,    // 行走
        Running     // 跑步
    }
    
    // 动画控制参数
    public bool AutoWalkAnimation { get; set; } = true;
    public float MoveThreshold { get; set; } = 0.01f;
    public float AnimationSpeed { get; set; } = 1.0f;
    
    // 移动检测相关
    private bool isMoving = false;
    private Vector3 lastPosition;
    private AnimationState currentAnimState = AnimationState.Idle;
    private string currentAnimationState = "待机";
    
    // 目标角色
    private OCIChar targetChar;
    
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public CharacterAnimationController(ManualLogSource logger)
    {
        this.logger = logger;
    }
    
    /// <summary>
    /// 设置目标角色
    /// </summary>
    /// <param name="character">目标角色</param>
    public void SetTargetCharacter(OCIChar character)
    {
        if (character != null)
        {
            targetChar = character;
            lastPosition = character.guideObject.transform.position;
            logger?.LogInfo($"设置动画控制目标角色: {character.oiCharInfo}");
        }
        else
        {
            targetChar = null;
            logger?.LogWarning("目标角色设置为空");
        }
    }
    
    /// <summary>
    /// 获取当前动画状态
    /// </summary>
    public AnimationState CurrentAnimationState => currentAnimState;
    
    /// <summary>
    /// 获取当前动画状态描述
    /// </summary>
    public string CurrentAnimationDescription => currentAnimationState;
    
    /// <summary>
    /// 获取是否正在移动
    /// </summary>
    public bool IsMoving => isMoving;
    
    /// <summary>
    /// 更新动画控制 - 在主循环中调用
    /// </summary>
    public void Update()
    {
        if (AutoWalkAnimation && targetChar != null)
        {
            UpdateAnimationBasedOnMovement();
        }
    }
    
    /// <summary>
    /// 基于移动状态自动更新动画
    /// </summary>
    private void UpdateAnimationBasedOnMovement()
    {
        if (targetChar == null) return;
        
        Vector3 currentPosition = targetChar.guideObject.transform.position;
        float distanceMoved = Vector3.Distance(currentPosition, lastPosition);
        
        // 检测是否在移动
        bool wasMoving = isMoving;
        isMoving = distanceMoved > MoveThreshold;
        
        // 如果移动状态发生变化，更新动画
        if (isMoving != wasMoving)
        {
            if (isMoving)
            {
                // 开始移动 - 播放行走动画
                logger?.LogInfo("角色开始移动 - 播放行走动画");
                SetAnimationState(AnimationState.Walking);
            }
            else
            {
                // 停止移动 - 播放待机动画
                logger?.LogInfo("角色停止移动 - 播放待机动画");
                SetAnimationState(AnimationState.Idle);
            }
        }
        
        lastPosition = currentPosition;
    }
    
    /// <summary>
    /// 手动设置动画状态
    /// </summary>
    /// <param name="state">动画状态</param>
    public void SetAnimationState(AnimationState state)
    {
        if (targetChar == null)
        {
            logger?.LogWarning("无法设置动画状态：目标角色为空");
            return;
        }
        
        try
        {
            switch (state)
            {
                case AnimationState.Idle:
                    targetChar.LoadAnime(0, 0, 0, 0.0f);
                    currentAnimationState = "待机";
                    logger?.LogInfo("设置动画状态: 待机");
                    break;
                case AnimationState.Walking:
                    targetChar.LoadAnime(0, 1, 0, 0.0f);
                    currentAnimationState = "行走";
                    logger?.LogInfo("设置动画状态: 行走");
                    break;
                case AnimationState.Running:
                    targetChar.LoadAnime(0, 1, 1, 0.0f);
                    currentAnimationState = "跑步";
                    logger?.LogInfo("设置动画状态: 跑步");
                    break;
            }
            
            targetChar.animeSpeed = AnimationSpeed;
            currentAnimState = state;
        }
        catch (Exception e)
        {
            logger?.LogError($"设置动画状态失败: {e.Message}");
        }
    }
    
    /// <summary>
    /// 循环演示动画（用于测试）
    /// </summary>
    public void DemonstrateAnimation()
    {
        if (targetChar == null)
        {
            logger?.LogWarning("无法演示动画：目标角色为空");
            return;
        }
        
        // 循环切换动画状态
        AnimationState nextState = currentAnimState switch
        {
            AnimationState.Idle => AnimationState.Walking,
            AnimationState.Walking => AnimationState.Running,
            AnimationState.Running => AnimationState.Idle,
            _ => AnimationState.Idle
        };
        
        SetAnimationState(nextState);
        logger?.LogInfo($"演示动画切换: {currentAnimationState}");
    }
    
    /// <summary>
    /// 设置动画播放速度
    /// </summary>
    /// <param name="speed">播放速度</param>
    public void SetAnimationSpeed(float speed)
    {
        AnimationSpeed = Mathf.Clamp(speed, 0.1f, 3.0f);
        if (targetChar != null)
        {
            targetChar.animeSpeed = AnimationSpeed;
        }
        logger?.LogInfo($"设置动画播放速度: {AnimationSpeed}");
    }
    
    /// <summary>
    /// 启用/禁用自动行走动画
    /// </summary>
    /// <param name="enabled">是否启用</param>
    public void SetAutoWalkAnimation(bool enabled)
    {
        AutoWalkAnimation = enabled;
        logger?.LogInfo($"自动行走动画: {(enabled ? "启用" : "禁用")}");
    }
    
    /// <summary>
    /// 设置移动检测阈值
    /// </summary>
    /// <param name="threshold">阈值</param>
    public void SetMoveThreshold(float threshold)
    {
        MoveThreshold = Mathf.Max(0.001f, threshold);
        logger?.LogInfo($"设置移动检测阈值: {MoveThreshold}");
    }
    
    /// <summary>
    /// 重置动画控制器状态
    /// </summary>
    public void Reset()
    {
        isMoving = false;
        currentAnimState = AnimationState.Idle;
        currentAnimationState = "待机";
        if (targetChar != null)
        {
            lastPosition = targetChar.guideObject.transform.position;
        }
        logger?.LogInfo("动画控制器状态已重置");
    }
    
    /// <summary>
    /// 获取动画控制器状态信息
    /// </summary>
    /// <returns>状态信息字符串</returns>
    public string GetStatusInfo()
    {
        if (targetChar == null)
        {
            return "未设置目标角色";
        }
        
        return $"动画状态: {currentAnimationState} | " +
               $"移动状态: {(isMoving ? "移动中" : "静止")} | " +
               $"自动控制: {(AutoWalkAnimation ? "开启" : "关闭")} | " +
               $"播放速度: {AnimationSpeed:F1}x";
    }
}
