
using BepInEx;
using UnityEngine;
using Studio;
using AIChara;

namespace MySecondPlugin
{
    [BepInPlugin("com.example.studiopowertools", "Studio Power Tools", "1.0.0")]
    public class StudioPowerTools : BaseUnityPlugin
    {
        // UI 可见性状态
        private bool isVisible = false;
        // UI 窗口的位置
        private Rect windowRect = new Rect(20, 20, 400, 550);

        // 当前选中的角色
        private OCIChar selectedChar;
        private ChaControl chaControl;

        // UI 输入字段的值
        private string posX = "0", posY = "0", posZ = "0";
        private string animationName = "";

        void Update()
        {
            // 按 F3 切换 UI 可见性
            if (Input.GetKeyDown(KeyCode.F3))
            {
                isVisible = !isVisible;
            }

            // 持续更新当前选中的角色
            UpdateSelectedCharacter();
        }

        void OnGUI()
        {
            if (!isVisible) return;

            // 绘制插件的主窗口
            windowRect = GUI.Window(0, windowRect, DrawWindow, "Studio Power Tools");
        }

        /// <summary>
        /// 绘制插件窗口及其所有控件
        /// </summary>
        void DrawWindow(int windowID)
        {
            GUI.DragWindow(new Rect(0, 0, 10000, 20));

            if (chaControl == null)
            {
                GUI.Label(new Rect(10, 25, 380, 30), "请在左侧层级视图中选择一个角色");
                return;
            }

            // 显示当前选中的角色名
            GUI.Label(new Rect(10, 25, 380, 30), $"当前角色: {chaControl.chaFile.parameter.fullname}");

            // --- 位置控制 ---
            GUI.Box(new Rect(10, 60, 380, 90), "位置控制 (Position)");
            GUI.Label(new Rect(20, 90, 20, 20), "X:");
            posX = GUI.TextField(new Rect(45, 90, 80, 20), posX);
            GUI.Label(new Rect(135, 90, 20, 20), "Y:");
            posY = GUI.TextField(new Rect(160, 90, 80, 20), posY);
            GUI.Label(new Rect(250, 90, 20, 20), "Z:");
            posZ = GUI.TextField(new Rect(275, 90, 80, 20), posZ);
            if (GUI.Button(new Rect(20, 120, 360, 20), "应用位置"))
            {
                ApplyPosition();
            }

            // --- 衣物控制 ---
            GUI.Box(new Rect(10, 160, 380, 60), "衣物控制 (Clothes)");
            if (GUI.Button(new Rect(20, 190, 175, 20), "全部显示"))
            {
                ShowAllClothes();
            }
            if (GUI.Button(new Rect(205, 190, 175, 20), "全部隐藏"))
            {
                HideAllClothes();
            }

            // --- 动画控制 ---
            GUI.Box(new Rect(10, 230, 380, 60), "动画控制 (Animation)");
            animationName = GUI.TextField(new Rect(20, 260, 250, 20), animationName);
            if (GUI.Button(new Rect(280, 260, 100, 20), "播放动画"))
            {
                PlayAnimation();
            }

            // --- 镜头控制 ---
            GUI.Box(new Rect(10, 300, 380, 90), "镜头控制 (Camera)");
            if (GUI.Button(new Rect(20, 330, 360, 20), "聚焦到角色头部"))
            {
                FocusCameraOnHead();
            }
            if (GUI.Button(new Rect(20, 360, 360, 20), "切换第一人称视角"))
            {
                ToggleFirstPersonView();
            }
        }

        /// <summary>
        /// 更新当前选中的角色信息
        /// </summary>
        void UpdateSelectedCharacter()
        {
            // 尝试从选中的节点获取角色
            var selectNode = Studio.Studio.Instance.treeNodeCtrl.selectNode;
            if (selectNode != null)
            {
                // 通过节点的objectCtrlInfo获取OCIChar
                var objectCtrlInfo = Studio.Studio.Instance.dicInfo.TryGetValue(selectNode, out var info) ? info : null;
                if (objectCtrlInfo is OCIChar ociChar)
                {
                    selectedChar = ociChar;
                    chaControl = selectedChar.charInfo;
                    // 当选择变更时，更新UI上的位置信息
                    if (GUI.GetNameOfFocusedControl() == "")
                    {
                        var pos = selectedChar.guideObject.transform.position;
                        posX = pos.x.ToString("F3");
                        posY = pos.y.ToString("F3");
                        posZ = pos.z.ToString("F3");
                    }
                    return;
                }
            }

            // 如果没有选中角色，尝试自动选择第一个角色
            foreach (var obj in Studio.Studio.Instance.dicObjectCtrl.Values)
            {
                if (obj is OCIChar autoChar)
                {
                    selectedChar = autoChar;
                    chaControl = selectedChar.charInfo;
                    if (GUI.GetNameOfFocusedControl() == "")
                    {
                        var pos = selectedChar.guideObject.transform.position;
                        posX = pos.x.ToString("F3");
                        posY = pos.y.ToString("F3");
                        posZ = pos.z.ToString("F3");
                    }
                    return;
                }
            }

            // 没有找到任何角色
            selectedChar = null;
            chaControl = null;
        }

        /// <summary>
        /// 应用UI中输入的位置
        /// </summary>
        void ApplyPosition()
        {
            if (selectedChar == null) return;
            try
            {
                float x = float.Parse(posX);
                float y = float.Parse(posY);
                float z = float.Parse(posZ);
                selectedChar.guideObject.transform.position = new Vector3(x, y, z);
            }
            catch (System.FormatException)
            {
                Debug.LogError("位置输入格式错误，请输入有效的浮点数。");
            }
        }

        /// <summary>
        /// 播放指定名称的动画
        /// </summary>
        void PlayAnimation()
        {
            if (selectedChar == null || string.IsNullOrEmpty(animationName)) return;
            try
            {
                // 尝试使用LoadAnime方法播放动画
                // 这里使用简单的动画切换，可以根据需要调整参数
                if (animationName.ToLower().Contains("idle") || animationName.ToLower().Contains("待机"))
                {
                    selectedChar.LoadAnime(0, 0, 0, 0.0f);
                }
                else if (animationName.ToLower().Contains("walk") || animationName.ToLower().Contains("行走"))
                {
                    selectedChar.LoadAnime(0, 1, 0, 0.0f);
                }
                else if (animationName.ToLower().Contains("run") || animationName.ToLower().Contains("跑步"))
                {
                    selectedChar.LoadAnime(0, 1, 1, 0.0f);
                }
                else
                {
                    // 默认播放待机动画
                    selectedChar.LoadAnime(0, 0, 0, 0.0f);
                }
                selectedChar.animeSpeed = 1.0f;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"播放动画失败: {e.Message}");
            }
        }

        /// <summary>
        /// 将相机聚焦到角色头部
        /// </summary>
        void FocusCameraOnHead()
        {
            if (chaControl == null) return;
            try
            {
                // 获取主相机
                Camera mainCamera = Camera.main;
                if (mainCamera == null)
                {
                    // 尝试查找Studio相机
                    mainCamera = GameObject.Find("StudioScene/Camera/Main Camera")?.GetComponent<Camera>();
                    if (mainCamera == null)
                    {
                        mainCamera = GameObject.Find("Camera/Main Camera")?.GetComponent<Camera>();
                    }
                }

                if (mainCamera != null)
                {
                    // 获取头部位置
                    Transform headBone = chaControl.objHeadBone?.transform;
                    if (headBone != null)
                    {
                        Vector3 headPos = headBone.position;
                        Vector3 cameraOffset = headBone.forward * -1.5f + headBone.up * 0.2f;

                        mainCamera.transform.position = headPos + cameraOffset;
                        mainCamera.transform.LookAt(headPos);
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"聚焦相机失败: {e.Message}");
            }
        }

        /// <summary>
        /// 切换第一人称视角
        /// </summary>
        void ToggleFirstPersonView()
        {
            if (chaControl == null) return;
            try
            {
                // 获取主相机
                Camera mainCamera = Camera.main;
                if (mainCamera == null)
                {
                    mainCamera = GameObject.Find("StudioScene/Camera/Main Camera")?.GetComponent<Camera>();
                    if (mainCamera == null)
                    {
                        mainCamera = GameObject.Find("Camera/Main Camera")?.GetComponent<Camera>();
                    }
                }

                if (mainCamera != null)
                {
                    Transform headBone = chaControl.objHeadBone?.transform;
                    if (headBone != null)
                    {
                        Vector3 headPos = headBone.position;

                        // 通过检查相机与头部的距离来判断是否已经是第一人称
                        if (Vector3.Distance(mainCamera.transform.position, headPos) < 0.5f)
                        {
                            // 退出第一人称，恢复默认跟随模式
                            FocusCameraOnHead();
                        }
                        else
                        {
                            // 进入第一人称 - 将相机移动到头部位置
                            mainCamera.transform.position = headPos + headBone.forward * 0.1f;
                            mainCamera.transform.rotation = headBone.rotation;
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"切换第一人称视角失败: {e.Message}");
            }
        }

        /// <summary>
        /// 显示所有衣物
        /// </summary>
        void ShowAllClothes()
        {
            if (selectedChar == null) return;
            try
            {
                // 显示所有衣物类型 (0-7)
                for (int i = 0; i < 8; i++)
                {
                    selectedChar.SetClothesState(i, 0); // 0 = 显示
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"显示衣物失败: {e.Message}");
            }
        }

        /// <summary>
        /// 隐藏所有衣物
        /// </summary>
        void HideAllClothes()
        {
            if (selectedChar == null) return;
            try
            {
                // 隐藏所有衣物类型 (0-7)
                for (int i = 0; i < 8; i++)
                {
                    selectedChar.SetClothesState(i, 2); // 2 = 隐藏
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"隐藏衣物失败: {e.Message}");
            }
        }
    }
}
