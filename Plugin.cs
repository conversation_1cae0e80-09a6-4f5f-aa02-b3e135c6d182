
using BepInEx;
using UnityEngine;
using Studio;
using AIChara;
using System.Linq;

namespace StudioPowerTools
{
    [BepInPlugin("com.example.studiopowertools", "Studio Power Tools", "1.0.0")]
    public class StudioPowerTools : BaseUnityPlugin
    {
        // UI 可见性状态
        private bool isVisible = false;
        // UI 窗口的位置
        private Rect windowRect = new Rect(20, 20, 400, 550);

        // 当前选中的角色
        private OCIChar selectedChar;
        private ChaControl chaControl;

        // UI 输入字段的值
        private string posX = "0", posY = "0", posZ = "0";
        private string animationName = "";

        void Update()
        {
            // 按 F3 切换 UI 可见性
            if (Input.GetKeyDown(KeyCode.F3))
            {
                isVisible = !isVisible;
            }

            // 持续更新当前选中的角色
            UpdateSelectedCharacter();
        }

        void OnGUI()
        {
            if (!isVisible) return;

            // 绘制插件的主窗口
            windowRect = GUI.Window(0, windowRect, DrawWindow, "Studio Power Tools");
        }

        /// <summary>
        /// 绘制插件窗口及其所有控件
        /// </summary>
        void DrawWindow(int windowID)
        {
            GUI.DragWindow(new Rect(0, 0, 10000, 20));

            if (chaControl == null)
            {
                GUI.Label(new Rect(10, 25, 380, 30), "请在左侧层级视图中选择一个角色");
                return;
            }

            // 显示当前选中的角色名
            GUI.Label(new Rect(10, 25, 380, 30), $"当前角色: {chaControl.chaFile.parameter.fullname}");

            // --- 位置控制 ---
            GUI.Box(new Rect(10, 60, 380, 90), "位置控制 (Position)");
            GUI.Label(new Rect(20, 90, 20, 20), "X:");
            posX = GUI.TextField(new Rect(45, 90, 80, 20), posX);
            GUI.Label(new Rect(135, 90, 20, 20), "Y:");
            posY = GUI.TextField(new Rect(160, 90, 80, 20), posY);
            GUI.Label(new Rect(250, 90, 20, 20), "Z:");
            posZ = GUI.TextField(new Rect(275, 90, 80, 20), posZ);
            if (GUI.Button(new Rect(20, 120, 360, 20), "应用位置"))
            {
                ApplyPosition();
            }

            // --- 衣物控制 ---
            GUI.Box(new Rect(10, 160, 380, 60), "衣物控制 (Clothes)");
            if (GUI.Button(new Rect(20, 190, 175, 20), "全部显示"))
            {
                chaControl.SetClothesState(0);
            }
            if (GUI.Button(new Rect(205, 190, 175, 20), "全部隐藏"))
            {
                chaControl.SetClothesState(2);
            }

            // --- 动画控制 ---
            GUI.Box(new Rect(10, 230, 380, 60), "动画控制 (Animation)");
            animationName = GUI.TextField(new Rect(20, 260, 250, 20), animationName);
            if (GUI.Button(new Rect(280, 260, 100, 20), "播放动画"))
            {
                PlayAnimation();
            }

            // --- 镜头控制 ---
            GUI.Box(new Rect(10, 300, 380, 90), "镜头控制 (Camera)");
            if (GUI.Button(new Rect(20, 330, 360, 20), "聚焦到角色头部"))
            {
                FocusCameraOnHead();
            }
            if (GUI.Button(new Rect(20, 360, 360, 20), "切换第一人称视角"))
            {
                ToggleFirstPersonView();
            }
        }

        /// <summary>
        /// 更新当前选中的角色信息
        /// </summary>
        void UpdateSelectedCharacter()
        {
            selectedChar = Studio.Studio.Instance.treeNodeCtrl.selectNode?.data as OCIChar;
            if (selectedChar != null)
            {
                chaControl = selectedChar.charInfo;
                // 当选择变更时，更新UI上的位置信息
                if (GUI.GetNameOfFocusedControl() == "")
                {
                    posX = chaControl.transform.position.x.ToString("F3");
                    posY = chaControl.transform.position.y.ToString("F3");
                    posZ = chaControl.transform.position.z.ToString("F3");
                }
            }
            else
            {
                chaControl = null;
            }
        }

        /// <summary>
        /// 应用UI中输入的位置
        /// </summary>
        void ApplyPosition()
        {
            if (chaControl == null) return;
            try
            {
                float x = float.Parse(posX);
                float y = float.Parse(posY);
                float z = float.Parse(posZ);
                chaControl.transform.position = new Vector3(x, y, z);
            }
            catch (System.FormatException)
            {
                Debug.LogError("位置输入格式错误，请输入有效的浮点数。");
            }
        }

        /// <summary>
        /// 播放指定名称的动画
        /// </summary>
        void PlayAnimation()
        {
            if (selectedChar == null || string.IsNullOrEmpty(animationName)) return;
            selectedChar.animeCtrl.Play(animationName);
        }

        /// <summary>
        /// 将相机聚焦到角色头部
        /// </summary>
        void FocusCameraOnHead()
        {
            if (chaControl == null) return;
            var cameraCtrl = Studio.Studio.Instance.cameraCtrl;
            Transform headBone = chaControl.cmpBoneBody.transform.Find("cf_j_head");
            if (cameraCtrl != null && headBone != null)
            { 
                cameraCtrl.SetTarget(headBone);
                cameraCtrl.distance = 1.5f; // 设置一个合适的观察距离
            }
        }

        /// <summary>
        /// 切换第一人称视角
        /// </summary>
        void ToggleFirstPersonView()
        {
            if (chaControl == null) return;
            var cameraCtrl = Studio.Studio.Instance.cameraCtrl;
            Transform headBone = chaControl.cmpBoneBody.transform.Find("cf_j_head");

            if (cameraCtrl != null && headBone != null)
            {
                // 通过检查相机与头部的距离来判断是否已经是第一人称
                if (Vector3.Distance(cameraCtrl.cameraPos, headBone.position) < 0.1f)
                {
                    // 退出第一人称，恢复默认跟随模式
                    FocusCameraOnHead();
                }
                else
                {
                    // 进入第一人称
                    cameraCtrl.SetTarget(headBone);
                    cameraCtrl.cameraPos = headBone.position;
                    // 可以根据需要微调相机角度，使其朝向角色正前方
                    cameraCtrl.cameraAngle = new Vector3(headBone.eulerAngles.x, headBone.eulerAngles.y, 0);
                }
            }
        }
    }
}
