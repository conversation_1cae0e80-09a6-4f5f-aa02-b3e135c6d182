using AIChara;
using BepInEx;
using BepInEx.Logging;
using Studio;
using UnityEngine;
using System;
using UniRx;
using System.Collections.Generic;

[BepInPlugin("com.yourname.characterinfoui", "CharacterInfoUI", "1.0.0")]
public class CharacterInfoUI : BaseUnityPlugin
{
    private bool _showUI = false;
    private Vector2 _scrollPosition = Vector2.zero;
    private OCIChar _selectedCharacter = null;
    private int _selectedObjectId = -1;
    
    // Cache frequently used values
    private const float PiOver180 = Mathf.PI / 180f;
    private float _angle = 0f;
    private float _lastUpdateTime = 0f;
    private const float UpdateInterval = 1.0f; // Update physics at 10Hz instead of every frame
    
    // Cache the clothes IDs to avoid recreating the list
    private static readonly int[] ClothesIds = {1, 2, 3, 4, 5, 6, 7};
    private byte _clothesState = 0;
    private const int ClothesStateChangeInterval = 5; // frames

    // 移动控制相关变量
    private bool _isLinearMoving = false;
    private bool _isCircularMoving = false;
    private bool _isSquareMoving = false;
    private bool _isFirstPersonMode = false;
    private float _moveSpeed = 2.0f;
    private float _circleRadius = 3.0f;
    private float _squareSize = 4.0f;
    private float _firstPersonMoveSpeed = 3.0f;

    // 移动状态变量
    private Vector3 _startPosition;
    private Vector3 _targetPosition;
    private float _moveTimer = 0f;
    private int _squareMoveStep = 0;
    
    private List<OCIChar> _cachedCharacters = new();
    private List<CharacterInfo> _characterList = new();
	private struct CharacterInfo
	{
		public int id;
		public string name;
		public Vector3 position;
	}
    
    private void Awake()
    {
        Logger.LogInfo("CharacterInfoUI plugin loaded");
    }

    private void Update()
    {
        // Only check for input every frame
        if (Input.GetKeyDown(KeyCode.F3))
        {
            _showUI = !_showUI;
        }

        // 每30帧更新一次角色缓存
        if (Time.frameCount % 30 == 0)
        {
            CacheCharacters();
        }

        // 自动选择第一个角色
        if (_cachedCharacters.Count > 0)
        {
            _selectedCharacter = _cachedCharacters[0];
        }

        if (_selectedCharacter == null) return;

        // 更新移动控制
        UpdateMovement();
    }

    private void OnGUI()
    {
        if (!_showUI) return;

        // Only create the window when needed
        GUI.Window(0, new Rect(10, 10, 350, 700), DrawUI, "Character Control UI");
    }

    private void DrawUI(int windowID)
    {
        GUILayout.BeginVertical();

        // 角色状态显示
        GUILayout.Label("=== 角色状态 ===");
        if (_selectedCharacter != null)
        {
            GUILayout.Label("角色: " + _selectedCharacter.oiCharInfo);
            GUILayout.Label("位置: " + _selectedCharacter.oiCharInfo.changeAmount.pos.ToString("F2"));
        }
        else
        {
            GUILayout.Label("正在搜索角色...");
            if (GUILayout.Button("手动刷新角色"))
            {
                CacheCharacters();
            }
        }

        GUILayout.Space(10);

        // 移动控制区域
        GUILayout.Label("=== 移动控制 ===");

        if (_selectedCharacter != null)
        {
            // 移动速度控制
            GUILayout.Label($"移动速度: {_moveSpeed:F1}");
            _moveSpeed = GUILayout.HorizontalSlider(_moveSpeed, 0.5f, 5.0f);

            GUILayout.Space(5);

            // 直线移动
            if (GUILayout.Button(_isLinearMoving ? "停止直线移动" : "开始直线移动"))
            {
                ToggleLinearMovement();
            }

            // 圆形移动
            GUILayout.Label($"圆形半径: {_circleRadius:F1}");
            _circleRadius = GUILayout.HorizontalSlider(_circleRadius, 1.0f, 5.0f);

            if (GUILayout.Button(_isCircularMoving ? "停止圆形移动" : "开始圆形移动"))
            {
                ToggleCircularMovement();
            }

            // 方形移动
            GUILayout.Label($"方形大小: {_squareSize:F1}");
            _squareSize = GUILayout.HorizontalSlider(_squareSize, 2.0f, 6.0f);

            if (GUILayout.Button(_isSquareMoving ? "停止方形移动" : "开始方形移动"))
            {
                ToggleSquareMovement();
            }

            GUILayout.Space(10);

            // 第一人称模式
            GUILayout.Label("=== 第一人称模式 ===");

            if (GUILayout.Button(_isFirstPersonMode ? "退出第一人称" : "进入第一人称"))
            {
                ToggleFirstPersonMode();
            }

            if (_isFirstPersonMode)
            {
                GUILayout.Label($"移动速度: {_firstPersonMoveSpeed:F1}");
                _firstPersonMoveSpeed = GUILayout.HorizontalSlider(_firstPersonMoveSpeed, 1.0f, 10.0f);
                GUILayout.Label("=== 第一人称控制 ===");
                GUILayout.Label("WASD键移动角色");
                GUILayout.Label("W-前进, S-后退");
                GUILayout.Label("A-左移, D-右移");
                GUILayout.Label("右键拖拽旋转视角");
                GUILayout.Label("(基于摄像机方向移动)");
            }

            GUILayout.Space(10);

            // 服装控制区域
            GUILayout.Label("=== 服装控制 ===");

            if (GUILayout.Button("全部穿上"))
            {
                SetClothesState(0); // 穿
            }

            if (GUILayout.Button("全部半脱"))
            {
                SetClothesState(1); // 半脱
            }

            if (GUILayout.Button("全部脱掉"))
            {
                SetClothesState(2); // 脱
            }

            GUILayout.Space(10);

            // 状态显示
            GUILayout.Label("=== 状态信息 ===");
            GUILayout.Label("移动状态: " + GetMovementStatus());
        }
        else
        {
            GUILayout.Label("请等待角色加载...");
        }

        GUILayout.EndVertical();
        GUI.DragWindow();
    }

    // 更新移动控制
    private void UpdateMovement()
    {
        if (_selectedCharacter == null) return;

        if (_isFirstPersonMode)
        {
            UpdateFirstPersonMovement();
        }
        else if (_isLinearMoving)
        {
            UpdateLinearMovement();
        }
        else if (_isCircularMoving)
        {
            UpdateCircularMovement();
        }
        else if (_isSquareMoving)
        {
            UpdateSquareMovement();
        }
    }

    // 切换直线移动
    private void ToggleLinearMovement()
    {
        if (_selectedCharacter == null) return;

        _isLinearMoving = !_isLinearMoving;
        if (_isLinearMoving)
        {
            // 停止其他移动
            _isCircularMoving = false;
            _isSquareMoving = false;
            _isFirstPersonMode = false;

            _startPosition = _selectedCharacter.oiCharInfo.changeAmount.pos;
            _targetPosition = _startPosition + Vector3.forward * 5f;
            _moveTimer = 0f;
        }
    }

    // 切换圆形移动
    private void ToggleCircularMovement()
    {
        if (_selectedCharacter == null) return;

        _isCircularMoving = !_isCircularMoving;
        if (_isCircularMoving)
        {
            // 停止其他移动
            _isLinearMoving = false;
            _isSquareMoving = false;
            _isFirstPersonMode = false;

            _startPosition = _selectedCharacter.oiCharInfo.changeAmount.pos;
            _moveTimer = 0f;
        }
    }

    // 切换方形移动
    private void ToggleSquareMovement()
    {
        if (_selectedCharacter == null) return;

        _isSquareMoving = !_isSquareMoving;
        if (_isSquareMoving)
        {
            // 停止其他移动
            _isLinearMoving = false;
            _isCircularMoving = false;
            _isFirstPersonMode = false;

            _startPosition = _selectedCharacter.oiCharInfo.changeAmount.pos;
            _squareMoveStep = 0;
            _moveTimer = 0f;
        }
    }

    // 切换第一人称模式
    private void ToggleFirstPersonMode()
    {
        if (_selectedCharacter == null) return;

        _isFirstPersonMode = !_isFirstPersonMode;
        if (_isFirstPersonMode)
        {
            // 停止其他移动
            _isLinearMoving = false;
            _isCircularMoving = false;
            _isSquareMoving = false;

            Logger.LogInfo("进入第一人称模式");
        }
        else
        {
            Logger.LogInfo("退出第一人称模式");
        }
    }

    // 更新直线移动
    private void UpdateLinearMovement()
    {
        _moveTimer += Time.deltaTime * _moveSpeed;
        float t = Mathf.PingPong(_moveTimer, 1f);
        Vector3 newPosition = Vector3.Lerp(_startPosition, _targetPosition, t);
        _selectedCharacter.oiCharInfo.changeAmount.pos = newPosition;
    }

    // 更新圆形移动
    private void UpdateCircularMovement()
    {
        _moveTimer += Time.deltaTime * _moveSpeed;
        float angle = _moveTimer;
        Vector3 offset = new Vector3(
            Mathf.Cos(angle) * _circleRadius,
            0,
            Mathf.Sin(angle) * _circleRadius
        );
        _selectedCharacter.oiCharInfo.changeAmount.pos = _startPosition + offset;
    }

    // 更新方形移动
    private void UpdateSquareMovement()
    {
        _moveTimer += Time.deltaTime * _moveSpeed;

        if (_moveTimer >= 1f)
        {
            _moveTimer = 0f;
            _squareMoveStep = (_squareMoveStep + 1) % 4;
        }

        Vector3 offset = Vector3.zero;
        float t = _moveTimer;

        switch (_squareMoveStep)
        {
            case 0: // 向右
                offset = Vector3.Lerp(Vector3.zero, Vector3.right * _squareSize, t);
                break;
            case 1: // 向前
                offset = Vector3.right * _squareSize + Vector3.Lerp(Vector3.zero, Vector3.forward * _squareSize, t);
                break;
            case 2: // 向左
                offset = Vector3.right * _squareSize + Vector3.forward * _squareSize + Vector3.Lerp(Vector3.zero, Vector3.left * _squareSize, t);
                break;
            case 3: // 向后
                offset = Vector3.forward * _squareSize + Vector3.Lerp(Vector3.zero, Vector3.back * _squareSize, t);
                break;
        }

        _selectedCharacter.oiCharInfo.changeAmount.pos = _startPosition + offset;
    }

    // 更新第一人称移动
    private void UpdateFirstPersonMovement()
    {
        if (_selectedCharacter == null) return;

        // 获取主摄像机
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            // 尝试查找Studio摄像机
            mainCamera = GameObject.Find("StudioScene/Camera/Main Camera")?.GetComponent<Camera>();
            if (mainCamera == null)
            {
                mainCamera = GameObject.Find("Camera/Main Camera")?.GetComponent<Camera>();
            }
        }

        if (mainCamera != null)
        {
            Vector3 cameraForward = mainCamera.transform.forward;
            Vector3 cameraRight = mainCamera.transform.right;
            Vector3 moveDirection = Vector3.zero;

            // WASD控制 - 基于摄像机方向
            if (Input.GetKey(KeyCode.W)) // 前进
            {
                moveDirection += cameraForward;
            }
            if (Input.GetKey(KeyCode.S)) // 后退
            {
                moveDirection -= cameraForward;
            }
            if (Input.GetKey(KeyCode.A)) // 左移
            {
                moveDirection -= cameraRight;
            }
            if (Input.GetKey(KeyCode.D)) // 右移
            {
                moveDirection += cameraRight;
            }

            // 只在水平面移动，忽略Y轴
            moveDirection.y = 0;

            // 应用移动
            if (moveDirection != Vector3.zero)
            {
                moveDirection.Normalize();
                Vector3 currentPos = _selectedCharacter.oiCharInfo.changeAmount.pos;
                Vector3 newPosition = currentPos + moveDirection * _firstPersonMoveSpeed * Time.deltaTime;
                _selectedCharacter.oiCharInfo.changeAmount.pos = newPosition;
            }

            // 鼠标控制摄像机旋转
            if (Input.GetMouseButton(1)) // 右键拖拽
            {
                float mouseX = Input.GetAxis("Mouse X") * 2.0f;
                float mouseY = Input.GetAxis("Mouse Y") * 2.0f;

                mainCamera.transform.Rotate(-mouseY, mouseX, 0);
            }
        }
    }

    // 设置服装状态
    private void SetClothesState(int state)
    {
        if (_selectedCharacter == null) return;

        try
        {
            // 使用OCIChar的SetClothesState方法
            foreach (var id in ClothesIds)
            {
                _selectedCharacter.SetClothesState(id, (Byte)state);
            }
            Logger.LogInfo($"设置所有服装状态为: {state}");
        }
        catch (System.Exception e)
        {
            Logger.LogError("设置服装状态失败: " + e.Message);
        }
    }

    // 获取移动状态
    private string GetMovementStatus()
    {
        if (_isFirstPersonMode) return "第一人称模式";
        if (_isLinearMoving) return "直线移动中";
        if (_isCircularMoving) return "圆形移动中";
        if (_isSquareMoving) return "方形移动中";
        return "静止";
    }

    private void CacheCharacters()
    {
        if (Studio.Studio.Instance == null) return;
        _cachedCharacters.Clear();
        foreach (var obj in Studio.Studio.Instance.dicObjectCtrl.Values)
        {
            if (obj is OCIChar ociChar)
            {
                _cachedCharacters.Add(ociChar);
            }
        }
    }
}