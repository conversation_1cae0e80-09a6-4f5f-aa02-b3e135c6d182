# CharacterAnimationController 使用说明

## 概述

`CharacterAnimationController` 是一个独立的角色动画控制类，专门用于HS2 Studio环境中的角色动画管理。它提供了自动动画检测和手动动画控制功能。

## 主要功能

### 1. 自动动画控制
- **自动行走检测**: 根据角色位置变化自动播放行走/待机动画
- **移动阈值设置**: 可调整移动检测的敏感度
- **智能状态切换**: 自动在待机和行走状态间切换

### 2. 手动动画控制
- **三种动画状态**: 待机、行走、跑步
- **动画速度控制**: 可调整动画播放速度 (0.1x - 3.0x)
- **即时状态切换**: 手动设置任意动画状态

### 3. 状态监控
- **实时状态显示**: 显示当前动画状态和移动状态
- **详细信息**: 提供完整的控制器状态信息

## 使用方法

### 基本初始化

```csharp
// 创建动画控制器实例
var animationController = new CharacterAnimationController(Logger);

// 设置目标角色
animationController.SetTargetCharacter(selectedCharacter);
```

### 在主循环中更新

```csharp
private void Update()
{
    // 更新动画控制器（必须在主循环中调用）
    animationController?.Update();
}
```

### 配置自动动画

```csharp
// 启用/禁用自动行走动画
animationController.SetAutoWalkAnimation(true);

// 设置移动检测阈值（默认0.01f）
animationController.SetMoveThreshold(0.02f);

// 设置动画播放速度
animationController.SetAnimationSpeed(1.5f);
```

### 手动控制动画

```csharp
// 设置特定动画状态
animationController.SetAnimationState(CharacterAnimationController.AnimationState.Walking);

// 演示动画循环
animationController.DemonstrateAnimation();
```

### 获取状态信息

```csharp
// 获取当前动画状态
var currentState = animationController.CurrentAnimationState;

// 获取是否正在移动
bool isMoving = animationController.IsMoving;

// 获取完整状态信息
string statusInfo = animationController.GetStatusInfo();
```

## 动画状态说明

| 状态 | 描述 | 动画参数 |
|------|------|----------|
| Idle | 待机状态 | LoadAnime(0, 0, 0, 0.0f) |
| Walking | 行走状态 | LoadAnime(0, 1, 0, 0.0f) |
| Running | 跑步状态 | LoadAnime(0, 1, 1, 0.0f) |

## 配置参数

### AutoWalkAnimation (bool)
- **默认值**: true
- **说明**: 是否启用自动行走动画检测

### MoveThreshold (float)
- **默认值**: 0.01f
- **范围**: 0.001f - 无限制
- **说明**: 移动检测阈值，值越小越敏感

### AnimationSpeed (float)
- **默认值**: 1.0f
- **范围**: 0.1f - 3.0f
- **说明**: 动画播放速度倍率

## 集成到现有插件

### 1. 添加字段
```csharp
private CharacterAnimationController _animationController;
```

### 2. 初始化
```csharp
private void Awake()
{
    _animationController = new CharacterAnimationController(Logger);
}
```

### 3. 设置目标角色
```csharp
// 当角色选择改变时
if (_selectedCharacter != previousCharacter)
{
    _animationController?.SetTargetCharacter(_selectedCharacter);
}
```

### 4. 更新循环
```csharp
private void Update()
{
    // 其他更新逻辑...
    
    // 更新动画控制器
    _animationController?.Update();
}
```

### 5. UI集成示例
```csharp
// 在OnGUI中添加动画控制界面
if (_animationController != null)
{
    GUILayout.Label(_animationController.GetStatusInfo());
    
    if (GUILayout.Button("切换自动动画"))
    {
        _animationController.SetAutoWalkAnimation(!_animationController.AutoWalkAnimation);
    }
    
    if (GUILayout.Button("演示动画"))
    {
        _animationController.DemonstrateAnimation();
    }
}
```

## 注意事项

1. **目标角色设置**: 必须先设置目标角色才能使用动画控制功能
2. **Update调用**: 必须在主循环中调用Update()方法以启用自动动画检测
3. **异常处理**: 控制器内部已包含异常处理，但建议在调用时进行空值检查
4. **性能考虑**: 自动动画检测每帧执行，对性能影响较小
5. **兼容性**: 适用于HS2 Studio环境，需要OCIChar对象支持

## 扩展功能

如需添加更多动画状态或自定义动画逻辑，可以：

1. 扩展 `AnimationState` 枚举
2. 在 `SetAnimationState` 方法中添加新的case
3. 根据需要调整自动检测逻辑

## 故障排除

### 动画不播放
- 检查目标角色是否正确设置
- 确认角色对象支持LoadAnime方法
- 查看日志输出的错误信息

### 自动检测不工作
- 确认AutoWalkAnimation已启用
- 检查MoveThreshold设置是否合适
- 确保Update()方法被正确调用

### 性能问题
- 考虑降低Update调用频率
- 调整移动检测阈值
- 禁用不需要的自动功能
